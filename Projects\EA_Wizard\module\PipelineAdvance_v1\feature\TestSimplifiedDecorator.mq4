//+------------------------------------------------------------------+
//| TestSimplifiedDecorator.mq4                                     |
//| 測試簡化的日誌裝飾者                                             |
//| 驗證基於 TradingPipelineContainerBase 的日誌裝飾者              |
//+------------------------------------------------------------------+
#property strict

// 包含簡化的實現
#include "SimplifiedTradingPipelineContainerLoggerDecorator.mqh"

//+------------------------------------------------------------------+
//| 腳本程序開始函數                                                 |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("=== 開始測試簡化的日誌裝飾者 ===");
    
    // 測試1：基本創建和使用
    TestBasicFunctionality();
    
    // 測試2：子流水線管理
    TestSubPipelineManagement();
    
    // 測試3：配置變更
    TestConfigurationChanges();
    
    Print("=== 簡化日誌裝飾者測試完成 ===");
}

//+------------------------------------------------------------------+
//| 測試基本功能                                                     |
//+------------------------------------------------------------------+
void TestBasicFunctionality()
{
    Print("\n--- 測試基本功能 ---");
    
    // 創建日誌記錄器
    CFileLog* logger = new CFileLog("SimplifiedDecoratorTest.log", INFO, true, false);
    
    if(logger == NULL)
    {
        Print("❌ 無法創建日誌記錄器");
        return;
    }
    
    // 創建日誌裝飾者容器
    TradingPipelineContainerLoggerDecorator* container = 
        new TradingPipelineContainerLoggerDecorator("TestContainer", 
                                                   "測試容器", 
                                                   logger,
                                                   "BasicTest",
                                                   false,
                                                   50,
                                                   true);
    
    if(container == NULL)
    {
        Print("❌ 無法創建日誌裝飾者容器");
        delete logger;
        return;
    }
    
    Print("✅ 成功創建日誌裝飾者容器");
    
    // 測試基本屬性
    Print("容器名稱: " + container.GetName());
    Print("容器類型: " + container.GetType());
    Print("容器描述: " + container.GetDescription());
    Print("是否啟用: " + (container.IsEnabled() ? "是" : "否"));
    Print("詳細日誌狀態: " + (container.IsDetailedLoggingEnabled() ? "啟用" : "禁用"));
    
    // 測試執行
    Print("執行前狀態: " + (container.IsExecuted() ? "已執行" : "未執行"));
    
    container.Execute();
    Print("執行後狀態: " + (container.IsExecuted() ? "已執行" : "未執行"));
    
    // 測試結果
    PipelineResult* result = container.GetResult();
    if(result != NULL)
    {
        Print("執行結果: " + (result.IsSuccess() ? "成功" : "失敗"));
        Print("結果消息: " + result.GetMessage());
    }
    
    // 測試重置
    container.Restore();
    Print("重置後狀態: " + (container.IsExecuted() ? "已執行" : "未執行"));
    
    // 清理
    delete container;
    delete logger;
    
    Print("✅ 基本功能測試完成");
}

//+------------------------------------------------------------------+
//| 測試子流水線管理                                                 |
//+------------------------------------------------------------------+
void TestSubPipelineManagement()
{
    Print("\n--- 測試子流水線管理 ---");
    
    // 創建日誌記錄器
    CFileLog* logger = new CFileLog("SubPipelineTest.log", DEBUG, true, false);
    
    // 創建主容器
    TradingPipelineContainerLoggerDecorator* mainContainer = 
        new TradingPipelineContainerLoggerDecorator("MainContainer", 
                                                   "主容器",
                                                   logger,
                                                   "MainContainer",
                                                   true,  // 擁有子流水線
                                                   10,
                                                   true);
    
    Print("主容器創建完成: " + mainContainer.GetName());
    
    // 創建子容器
    CFileLog* subLogger1 = new CFileLog("SubContainer1.log", INFO, false, false);
    CFileLog* subLogger2 = new CFileLog("SubContainer2.log", INFO, false, false);
    
    TradingPipelineContainerLoggerDecorator* subContainer1 = 
        new TradingPipelineContainerLoggerDecorator("SubContainer1", 
                                                   "子容器1",
                                                   subLogger1,
                                                   "SubContainer",
                                                   false,
                                                   5,
                                                   false);
    
    TradingPipelineContainerLoggerDecorator* subContainer2 = 
        new TradingPipelineContainerLoggerDecorator("SubContainer2", 
                                                   "子容器2",
                                                   subLogger2,
                                                   "SubContainer",
                                                   false,
                                                   5,
                                                   false);
    
    // 添加子流水線
    bool addResult1 = mainContainer.AddPipeline(subContainer1);
    bool addResult2 = mainContainer.AddPipeline(subContainer2);
    
    Print("添加子容器1結果: " + (addResult1 ? "成功" : "失敗"));
    Print("添加子容器2結果: " + (addResult2 ? "成功" : "失敗"));
    Print("子流水線數量: " + IntegerToString(mainContainer.GetPipelineCount()));
    
    // 執行主容器（會自動執行所有子流水線）
    mainContainer.Execute();
    
    // 重置
    mainContainer.Restore();
    
    // 清理（主容器會自動清理子容器，因為 owned=true）
    delete mainContainer;
    delete subLogger1;
    delete subLogger2;
    delete logger;
    
    Print("✅ 子流水線管理測試完成");
}

//+------------------------------------------------------------------+
//| 測試配置變更                                                     |
//+------------------------------------------------------------------+
void TestConfigurationChanges()
{
    Print("\n--- 測試配置變更 ---");
    
    // 創建日誌記錄器
    CFileLog* logger = new CFileLog("ConfigTest.log", DEBUG, true, false);
    
    // 創建容器（初始禁用詳細日誌）
    TradingPipelineContainerLoggerDecorator* container = 
        new TradingPipelineContainerLoggerDecorator("ConfigContainer", 
                                                   "配置測試容器",
                                                   logger,
                                                   "ConfigTest",
                                                   false,
                                                   50,
                                                   false);
    
    // 測試初始狀態
    Print("初始詳細日誌狀態: " + (container.IsDetailedLoggingEnabled() ? "啟用" : "禁用"));
    
    // 執行一次（應該只有基本日誌）
    container.Execute();
    
    // 啟用詳細日誌
    container.SetDetailedLogging(true);
    Print("變更後詳細日誌狀態: " + (container.IsDetailedLoggingEnabled() ? "啟用" : "禁用"));
    
    // 重置並再次執行（應該有詳細日誌）
    container.Restore();
    container.Execute();
    
    // 禁用詳細日誌
    container.SetDetailedLogging(false);
    Print("再次變更後詳細日誌狀態: " + (container.IsDetailedLoggingEnabled() ? "啟用" : "禁用"));
    
    // 最後一次執行
    container.Restore();
    container.Execute();
    
    // 測試其他配置
    container.SetEnabled(false);
    Print("禁用後狀態: " + (container.IsEnabled() ? "啟用" : "禁用"));
    
    container.SetEnabled(true);
    Print("重新啟用後狀態: " + (container.IsEnabled() ? "啟用" : "禁用"));
    
    // 測試描述變更
    container.SetDescription("更新的描述");
    Print("更新後描述: " + container.GetDescription());
    
    // 清理
    delete container;
    delete logger;
    
    Print("✅ 配置變更測試完成");
}

//+------------------------------------------------------------------+
//| 測試錯誤處理                                                     |
//+------------------------------------------------------------------+
void TestErrorHandling()
{
    Print("\n--- 測試錯誤處理 ---");
    
    // 測試空日誌記錄器
    TradingPipelineContainerLoggerDecorator* nullLoggerContainer = 
        new TradingPipelineContainerLoggerDecorator("NullLoggerContainer", 
                                                   "空日誌記錄器容器",
                                                   NULL,  // 空日誌記錄器
                                                   "ErrorTest",
                                                   false,
                                                   50,
                                                   true);
    
    Print("空日誌記錄器容器創建: " + (nullLoggerContainer != NULL ? "成功" : "失敗"));
    
    if(nullLoggerContainer != NULL)
    {
        // 嘗試執行（應該安全處理空日誌記錄器）
        nullLoggerContainer.Execute();
        nullLoggerContainer.Restore();
        
        delete nullLoggerContainer;
        Print("✅ 空日誌記錄器處理正常");
    }
    
    Print("✅ 錯誤處理測試完成");
}
